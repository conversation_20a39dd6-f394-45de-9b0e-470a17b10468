-- Create email verification tokens table for link-based email verification
-- This replaces the OTP-based verification system with a more user-friendly link-based approach

CREATE TABLE IF NOT EXISTS `email_verification_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `verification_token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `signup_data` text,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  UNIQUE KEY `unique_token` (`verification_token`),
  KEY `idx_email_verified` (`email`, `is_verified`),
  KEY `idx_token` (`verification_token`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add comment to table
ALTER TABLE `email_verification_tokens` COMMENT = 'Stores email verification tokens for link-based email verification during user signup';

-- Add comments to columns
ALTER TABLE `email_verification_tokens` 
  MODIFY COLUMN `email` varchar(255) NOT NULL COMMENT 'User email address for verification',
  MODIFY COLUMN `verification_token` varchar(255) NOT NULL COMMENT 'Secure verification token (URL-safe)',
  MODIFY COLUMN `expires_at` datetime NOT NULL COMMENT 'Token expiration timestamp (UTC)',
  MODIFY COLUMN `signup_data` text COMMENT 'JSON data containing signup information (encrypted password, workspace name, etc.)',
  MODIFY COLUMN `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether the token has been used for verification',
  MODIFY COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Token creation timestamp',
  MODIFY COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp';

-- Create indexes for performance
-- Note: UNIQUE indexes are already created above, these are additional performance indexes
CREATE INDEX `idx_email_expires_verified` 
  ON `email_verification_tokens` (`email`, `expires_at`, `is_verified`);

CREATE INDEX `idx_token_expires` 
  ON `email_verification_tokens` (`verification_token`, `expires_at`);

-- Insert a test record to verify table structure (will be cleaned up by the cleanup process)
-- This is commented out by default - uncomment for testing
-- INSERT INTO `email_verification_tokens` (`email`, `verification_token`, `expires_at`, `signup_data`, `is_verified`) 
-- VALUES ('<EMAIL>', 'test_token_12345', DATE_ADD(NOW(), INTERVAL 5 MINUTE), '{"test": true}', 0);

-- Show table structure for verification
DESCRIBE `email_verification_tokens`;

-- Show indexes for verification
SHOW INDEX FROM `email_verification_tokens`;

-- Display success message
SELECT 'Email verification tokens table created successfully!' as message;
