#!/bin/bash

# Email Link Verification Setup Script
# This script helps set up the new email link verification system

set -e  # Exit on any error

echo "🚀 Setting up Email Link Verification System"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running from correct directory
if [ ! -f "app/config.py" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_info "Step 1: Checking prerequisites..."

# Check if MySQL is accessible
if command -v mysql &> /dev/null; then
    print_status "MySQL client found"
else
    print_error "MySQL client not found. Please install MySQL client."
    exit 1
fi

# Check if Python is accessible
if command -v python3 &> /dev/null; then
    print_status "Python 3 found"
else
    print_error "Python 3 not found. Please install Python 3."
    exit 1
fi

print_info "Step 2: Creating database table..."

# Prompt for database credentials
echo ""
read -p "Enter MySQL username: " DB_USER
read -s -p "Enter MySQL password: " DB_PASS
echo ""
read -p "Enter database name: " DB_NAME

# Test database connection
if mysql -u "$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
    print_status "Database connection successful"
else
    print_error "Failed to connect to database. Please check credentials."
    exit 1
fi

# Run database migration
print_info "Creating email_verification_tokens table..."
if mysql -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < scripts/email_verification_tokens_table.sql; then
    print_status "Database table created successfully"
else
    print_error "Failed to create database table"
    exit 1
fi

print_info "Step 3: Checking environment configuration..."

# Check if .env file exists
if [ -f ".env" ]; then
    print_status ".env file found"
    
    # Check for required environment variables
    if grep -q "BASE_URL" .env; then
        print_status "BASE_URL configuration found"
    else
        print_warning "BASE_URL not found in .env file"
        echo ""
        read -p "Enter your base URL (e.g., http://127.0.0.1:8000): " BASE_URL
        echo "BASE_URL=$BASE_URL" >> .env
        print_status "BASE_URL added to .env file"
    fi
    
    # Check AWS SES configuration
    if grep -q "AWS_SES_REGION" .env && grep -q "AWS_SES_ACCESS_KEY_ID" .env; then
        print_status "AWS SES configuration found"
    else
        print_warning "AWS SES configuration incomplete"
        print_info "Please ensure these variables are set in your .env file:"
        echo "  - AWS_SES_REGION"
        echo "  - AWS_SES_ACCESS_KEY_ID"
        echo "  - AWS_SES_SECRET_ACCESS_KEY"
        echo "  - AWS_SES_SENDER_EMAIL"
    fi
    
else
    print_error ".env file not found. Please create one with required configuration."
    exit 1
fi

print_info "Step 4: Validating Python dependencies..."

# Check if required packages are installed
python3 -c "
import sys
try:
    import secrets
    import aioboto3
    import fastapi
    print('✅ Required packages found')
except ImportError as e:
    print(f'❌ Missing package: {e}')
    sys.exit(1)
" || exit 1

print_info "Step 5: Running validation tests..."

# Create a simple validation script
cat > temp_validation.py << 'EOF'
import sys
import os
sys.path.append('.')

try:
    # Test imports
    from app.core.services.email_verification_service import EmailVerificationService
    from app.core.services.auth.email_verification import EmailVerificationAuthService
    from app.apis.auth.email_verification import verify_email
    
    print("✅ All new modules import successfully")
    
    # Test token generation
    service = EmailVerificationService()
    token = service.generate_verification_token()
    
    if len(token) >= 64:
        print("✅ Token generation working correctly")
    else:
        print("❌ Token generation issue - token too short")
        sys.exit(1)
        
    print("✅ Email verification system validation passed")
    
except Exception as e:
    print(f"❌ Validation failed: {e}")
    sys.exit(1)
EOF

if python3 temp_validation.py; then
    print_status "Validation tests passed"
else
    print_error "Validation tests failed"
    rm -f temp_validation.py
    exit 1
fi

# Clean up
rm -f temp_validation.py

print_info "Step 6: Setting up Celery tasks..."

# Check if Celery is configured
if grep -q "send_verification_email_task" app/core/services/celery_conf/tasks.py; then
    print_status "Celery tasks configured"
else
    print_error "Celery tasks not properly configured"
    exit 1
fi

echo ""
echo "🎉 Email Link Verification System Setup Complete!"
echo "================================================="
echo ""
print_status "Database table created: email_verification_tokens"
print_status "New API endpoints available:"
echo "  - GET  /api/verify-email?token=<token>"
echo "  - POST /api/resend-verification-link"
print_status "Modified API endpoint:"
echo "  - POST /api/signup (now sends verification links)"
echo ""
print_info "Next Steps:"
echo "1. Restart your application server"
echo "2. Test the signup flow with a real email address"
echo "3. Check the verification email and click the link"
echo "4. Review the testing guide: docs/email_link_verification_testing.md"
echo ""
print_warning "Important Notes:"
echo "- Old OTP endpoints are still available but deprecated"
echo "- Make sure your BASE_URL is accessible from where users will click links"
echo "- Verify AWS SES is properly configured and sender email is verified"
echo "- Monitor application logs for any issues"
echo ""
print_info "For detailed testing instructions, see:"
echo "docs/email_link_verification_testing.md"
echo ""
echo "Happy testing! 🚀"
