import logging
import boto3
from collections import defaultdict
from typing import Dict, List
from app.core.services.api_cloud_providers.aws.aws_service_factory import AWSServiceFactory

# Get logger instance
logger = logging.getLogger(__name__)

class ResourceExplorerService:
    """Service to discover AWS regions containing resources for enabled services."""

    # Services that can be searched directly using service-based filtering
    SUPPORTED_SERVICES = ['ec2', 'rds', 'lambda', 'ecs', 'eks', 's3', 'iam', 'elb', 'efs', 'elasticache']
    
    def __init__(self, credentials: dict, region: str = 'ap-south-1'):
        """
        Lightweight initializer; no session initialization here.
        """
        self.credentials = credentials
        self.region = region
    
    @staticmethod
    def _extract_region_from_arn(arn: str) -> str:
        try:
            parts = arn.split(':')
            return parts[3] if len(parts) >= 4 and parts[3] else 'global'
        except Exception:
            return 'unknown'
    
    async def discover_regions_for_services(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Discover regions containing resources for enabled services using service-based filtering.
        Primary strategy: Use AWS Resource Explorer with service: filter
        Fallback strategy: Return empty list for services with no resources (will show as PASS)
        """
        service_region_mapping = defaultdict(set)
        # logger.info(f"🔍 Starting region discovery for {len(enabled_services)} enabled services")

        try:
            # Create resource-explorer-2 client specifically in ap-south-1 (sync boto3)
            boto_session = boto3.Session(
                aws_access_key_id=self.credentials.get('access_key'),
                aws_secret_access_key=self.credentials.get('secret_key'),
                aws_session_token=self.credentials.get('session_token'),
                region_name='ap-south-1',
            )

            client = boto_session.client('resource-explorer-2', region_name='ap-south-1')
            # logger.info("🔍 Using Resource Explorer service-based filtering...")

            for service in enabled_services:
                service_name = service['name']
                if service_name not in self.SUPPORTED_SERVICES:
                    logger.warning(f"⚠️ Service '{service_name}' not supported for region discovery")
                    continue

                # logger.info(f"🔍 Searching {service_name} resources...")

                try:
                    # Use service-based search (primary approach)
                    response = client.search(QueryString=f'service:{service_name}', MaxResults=100)
                    resources = response.get('Resources', [])

                    regions = set()
                    for resource in resources:
                        region = resource.get('Region')
                        if region:
                            regions.add(region)

                    service_region_mapping[service_name] = regions
                    logger.info(f"  ✅ {service_name.upper()}: {len(resources)} resources in {list(regions)}")

                except Exception as e:
                    logger.error(f"  ❌ {service_name}: Error - {e}")
                    # Service with error gets empty region list (will show as PASS)
                    service_region_mapping[service_name] = set()

        except Exception as e:
            logger.error(f"❌ Resource Explorer not available or failed: {e}")
            logger.info("🔄 Falling back to empty region mapping (services will show as PASS)")
            # If Resource Explorer fails entirely, all services get empty regions
            for service in enabled_services:
                service_region_mapping[service['name']] = set()

        # Convert to final format
        final_mapping = {service: list(regions) for service, regions in service_region_mapping.items()}

        # logger.info("📊 Final Service-Region Mapping:")
        for service, regions in final_mapping.items():
            if regions:
                logger.info(f"  '{service.upper()}': {regions}")
            else:
                logger.info(f"  '{service.upper()}': No regions found")

        return final_mapping
    
    async def get_regions_for_scan(self, enabled_services: List[Dict]) -> List[str]:
        """
        Get all unique regions that contain resources for any enabled service.
        """
        service_region_mapping = await self.discover_regions_for_services(enabled_services)
        
        # Collect all unique regions
        all_regions = set()
        for regions in service_region_mapping.values():
            all_regions.update(regions)
        
        unique_regions = sorted(list(all_regions))
        logger.info(f"🌍 Total unique regions for scan: {unique_regions}")
        
        return unique_regions
    
    async def get_service_region_mapping(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Get detailed mapping of services to their regions.
        """
        return await self.discover_regions_for_services(enabled_services)

    async def get_regions_for_service(self, service_name: str) -> List[str]:
        """
        Get regions containing resources for a specific service.
        This is the key method for service-specific region filtering.

        Args:
            service_name: Name of the AWS service (e.g., 'ec2', 'ecs', 'rds')

        Returns:
            List of regions where the service has resources, empty list if none found
        """
        service_region_mapping = await self.discover_regions_for_services([{'name': service_name}])
        return service_region_mapping.get(service_name, [])

    async def get_optimized_session_factory(self, credentials: dict, service_name: str) -> tuple:
        """
        Create an optimized session factory that only creates sessions for regions
        where the service actually has resources.

        Args:
            credentials: AWS credentials
            service_name: Name of the AWS service

        Returns:
            Tuple of (session_factory_function, filtered_regions_list)
        """
        from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

        # Get regions where this service has resources
        service_regions = await self.get_regions_for_service(service_name)

        logger.info(f"🎯 Service '{service_name}' has resources in regions: {service_regions}")

        if not service_regions:
            logger.info(f"📭 No resources found for service '{service_name}' - will show all checks as PASS")
            # Return empty session factory and empty regions
            def empty_session_factory(region: str):
                return None
            return empty_session_factory, []

        # Create base processor for session creation
        base = BaseChecksProcessor(credentials, service_regions)

        def optimized_session_factory(region: str):
            """Session factory that only creates sessions for regions with resources."""
            if region not in service_regions:
                logger.warning(f"⚠️ Skipping session creation for region '{region}' - no {service_name} resources found")
                return None
            return base.get_session(region)

        return optimized_session_factory, service_regions
