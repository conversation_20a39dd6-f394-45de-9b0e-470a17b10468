from app import app
from app.core.models.mysql import (get_user_data, add_new_user, add_workspace, update_workspace_user, update_user_admin,
                                   add_user_role)
from app.common import (UserExistsException, InternalServerException, get_utc_timestamp,
                        PredefinedUserRoleEnum, OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException,
                        OTPResendCooldownException)
from app.common.slack import send_slack_message
from app.core.services.email_verification_service import EmailVerificationService
from app.core.services.celery_conf.tasks import send_verification_email_task
import logging

__all__ = ['EmailVerificationAuthService', 'ResendVerificationLinkService']

logger = logging.getLogger(__name__)


class EmailVerificationAuthService:
    """
    Service to verify email verification tokens and complete user registration
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.verification_service = EmailVerificationService()

    async def process(self):
        """
        Verify token and create user account if valid
        """
        try:
            # Verify token and get signup data
            verification_data = await self.verification_service.verify_token(self.message.token)
            
            email = verification_data['email']
            
            # Check if user was created in the meantime (race condition protection)
            user = await get_user_data(self.conn_pool, type('obj', (object,), {'email': email})())
            if user and user.get("email") and user["email"].lower() == email.lower():
                raise UserExistsException("User with this email already exists")

            # Create user account with verified email
            await self._create_user_account(verification_data)

            logger.info(f"User account created successfully after email verification: {email}")
            
            return {
                "message": "Email verified and account created successfully",
                "email": email
            }

        except (OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException) as e:
            logger.warning(f"Email verification failed for token: {str(e)}")
            raise e
        
        except UserExistsException as e:
            logger.warning(f"User already exists during email verification")
            raise e
        
        except Exception as e:
            logger.error(f"Email verification process failed: {str(e)}")
            raise InternalServerException("Account creation failed. Please try again.")

    async def _create_user_account(self, verification_data):
        """
        Create user account with the provided verification data
        """
        try:
            # Create workspace
            workspace_id = await add_workspace(
                self.conn_pool, 
                verification_data['workspace_name'], 
                get_utc_timestamp()
            )
            
            # Create user
            user_id = await add_new_user(
                self.conn_pool,
                verification_data['email'],
                verification_data['password_hash'],  # Already encrypted in verification service
                workspace_id,
                get_utc_timestamp(),
                None,  # created_by is None for initial signup
                verification_data.get('first_name'),
                verification_data.get('last_name')
            )
            
            # Set up workspace ownership
            await update_workspace_user(self.conn_pool, user_id, workspace_id)
            await update_user_admin(self.conn_pool, user_id)
            await add_user_role(self.conn_pool, user_id, PredefinedUserRoleEnum.OWNER.value)

            # Send Slack notification (non-blocking)
            try:
                username = ""
                if verification_data.get('first_name') or verification_data.get('last_name'):
                    first_name = verification_data.get('first_name') or ""
                    last_name = verification_data.get('last_name') or ""
                    username = f"{first_name} {last_name}".strip()
                else:
                    username = verification_data['email']

                await send_slack_message(
                    f"`{verification_data['workspace_name']}` workspace created by `{username}`."
                )
            except Exception as slack_error:
                logger.warning(f"Failed to send Slack notification for user signup: {slack_error}")

        except Exception as e:
            logger.error(f"Failed to create user account: {str(e)}")
            raise InternalServerException("Failed to create user account")


class ResendVerificationLinkService:
    """
    Service to resend verification link
    """
    def __init__(self, message):
        self.message = message
        self.conn_pool = app.state.connection_pool
        self.verification_service = EmailVerificationService()

    async def process(self):
        """
        Resend verification link for the given email
        """
        try:
            # Resend verification link
            new_token = await self.verification_service.resend_verification_link(self.message.email)
            
            # Generate new verification URL
            verification_url = self.verification_service.generate_verification_url(new_token)
            
            # Get existing verification data to get first name
            verification_status = await self.verification_service.get_verification_status(self.message.email)
            first_name = None
            if verification_status:
                # Try to extract first name from signup data if available
                try:
                    import json
                    from app.core.models.mysql import get_verification_token_by_email
                    verification_record = await get_verification_token_by_email(self.conn_pool, self.message.email)
                    if verification_record and verification_record.get('signup_data'):
                        signup_data = json.loads(verification_record['signup_data'])
                        first_name = signup_data.get('first_name')
                except Exception:
                    pass  # If we can't get first name, it's okay
            
            # Send new verification email asynchronously using Celery
            send_verification_email_task.delay(
                recipient_email=self.message.email,
                verification_url=verification_url,
                first_name=first_name
            )

            logger.info(f"Verification link resent for email: {self.message.email}")

            return {
                "message": "New verification link sent to your email",
                "email": self.message.email,
                "expires_in_minutes": app.config.OTP_EXPIRY_MINUTES
            }

        except (OTPResendCooldownException, OTPNotFoundException) as e:
            logger.warning(f"Resend verification link failed for {self.message.email}: {str(e)}")
            raise e
        
        except Exception as e:
            logger.error(f"Resend verification link process failed: {str(e)}")
            raise InternalServerException("Failed to resend verification link. Please try again.")
