import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil
import logging

from app.common import AWSServiceNameEnum, AWSRegionNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

logger = logging.getLogger(__name__)

CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "s3")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"s3_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "s3")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _get_bucket_region(client, bucket_name: str) -> str:
    """Return the actual region of the given bucket.
    Fallbacks to us-east-1 when AWS returns null (classic behavior).
    """
    try:
        loc = await client.get_bucket_location(Bucket=bucket_name)
        location_constraint = loc.get("LocationConstraint")
        # Per AWS, LocationConstraint can be None for us-east-1
        if not location_constraint:
            return "us-east-1"
        # Some older responses may return legacy identifiers like EU
        if location_constraint == "EU":
            return "eu-west-1"
        return location_constraint
    except Exception:
        # If we cannot determine, default to us-east-1 rather than the client's region
        return "us-east-1"


async def _collect_region_resources(session, region: str, aws_account_id: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of S3-related resources that checks can consume.
    Note: S3 is global but buckets have regions, so we collect all data in us-east-1
    """

    # S3 is global service, but we collect data in us-east-1 and replicate to other regions
    async with session.client(AWSServiceNameEnum.S3.value) as s3_client, \
            session.client('s3control', region_name=AWSRegionNameEnum.US_WEST_2.value) as s3control_client:

        # Get all buckets (global operation)
        buckets = await s3_client.list_buckets()

        # Collect bucket-specific data
        bucket_details = {}
        for bucket in buckets.get("Buckets", []):
            bucket_name = bucket["Name"]
            bucket_region = await _get_bucket_region(s3_client, bucket_name)

            bucket_info = {
                "bucket": bucket,
                "region": bucket_region,
                "public_access_block": None,
                "acl": None,
                "lifecycle_configuration": None,
                "policy": None,
                "logging": None
            }

            try:
                # Get public access block configuration
                public_access_block = await s3_client.get_public_access_block(Bucket=bucket_name)
                bucket_info["public_access_block"] = public_access_block
            except Exception:
                pass

            try:
                # Get bucket ACL
                acl = await s3_client.get_bucket_acl(Bucket=bucket_name)
                bucket_info["acl"] = acl
            except Exception:
                pass

            try:
                # Get lifecycle configuration
                lifecycle_config = await s3_client.get_bucket_lifecycle_configuration(Bucket=bucket_name)
                bucket_info["lifecycle_configuration"] = lifecycle_config
            except Exception:
                pass

            try:
                # Get bucket policy
                bucket_policy = await s3_client.get_bucket_policy(Bucket=bucket_name)
                bucket_info["policy"] = bucket_policy
            except Exception:
                pass

            try:
                # Get logging configuration
                logging_config = await s3_client.get_bucket_logging(Bucket=bucket_name)
                bucket_info["logging"] = logging_config
            except Exception:
                pass

            bucket_details[bucket_name] = bucket_info

        # Get access points
        access_points = []
        try:
            access_points_response = await s3control_client.list_access_points(
                AccountId=aws_account_id
            )
            access_points = access_points_response.get("AccessPointList", [])

            # Get detailed info for each access point
            for access_point in access_points:
                access_point_name = access_point["Name"]
                try:
                    policy_status = await s3control_client.get_access_point_policy_status(
                        AccountId=aws_account_id,
                        Name=access_point_name
                    )
                    access_point["policy_status"] = policy_status
                except Exception:
                    access_point["policy_status"] = None
        except Exception:
            pass

        # Get multi-region access points
        multi_region_access_points = []
        try:
            multi_region_access_points_response = await s3control_client.list_multi_region_access_points(
                AccountId=aws_account_id
            )
            multi_region_access_points = multi_region_access_points_response.get("AccessPoints", [])

            # Get detailed info for each multi-region access point
            for access_point in multi_region_access_points:
                access_point_name = access_point["Name"]
                try:
                    policy_status = await s3control_client.get_multi_region_access_point_policy_status(
                        AccountId=aws_account_id,
                        Name=access_point_name
                    )
                    access_point["policy_status"] = policy_status
                except Exception:
                    access_point["policy_status"] = None
        except Exception:
            pass

    return {
        "region": region,
        "buckets": buckets,
        "bucket_details": bucket_details,
        "access_points": access_points,
        "multi_region_access_points": multi_region_access_points,
    }


async def fetch_and_cache_s3_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch S3 resources for a region and cache to JSON file `s3_<region>.json`.
    Returns the absolute file path for the region cache.
    Note: S3 is global, so we collect all data in us-east-1 and replicate to other regions.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)

    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path

    # For S3, we collect all data in us-east-1 and replicate to other regions
    if region == "us-east-1":
        data = await _collect_region_resources(session, region, aws_account_id)
    else:
        # For other regions, check if us-east-1 cache exists and copy it
        us_east_1_cache = _region_cache_path(workspace_id, aws_account_id, "us-east-1")
        if os.path.exists(us_east_1_cache):
            # Copy us-east-1 data and update region field
            with open(us_east_1_cache, "r") as fp:
                data = json.load(fp)
            data["region"] = region
        else:
            # Fallback: collect data for this region
            data = await _collect_region_resources(session, region, aws_account_id)

    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_s3_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    For S3, we prioritize us-east-1 first, then replicate to other regions.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []

    # Ensure us-east-1 is processed first for S3 global service pattern
    if "us-east-1" in regions:
        regions.remove("us-east-1")
        regions.insert(0, "us-east-1")

    # Process us-east-1 first, then others
    cache_paths = []
    if regions and regions[0] == "us-east-1":
        us_east_1_path = await fetch_and_cache_s3_region_data("us-east-1", session_factory, workspace_id, aws_account_id)
        cache_paths.append(us_east_1_path)
        remaining_regions = regions[1:]
    else:
        remaining_regions = regions

    # Process remaining regions concurrently
    if remaining_regions:
        tasks = [fetch_and_cache_s3_region_data(region, session_factory, workspace_id, aws_account_id) for region in remaining_regions]
        remaining_paths = await asyncio.gather(*tasks)
        cache_paths.extend(remaining_paths)

    return cache_paths


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Enhanced wrapper function with Resource Explorer optimization for S3 service.
    Uses Resource Explorer to only fetch data from regions where S3 resources exist.
    Note: S3 is a global service but buckets have regional locations.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id

    # If no regions provided (Resource Explorer found no S3 resources), return empty list
    if not regions:
        logger.info("📭 S3: No regions with resources - skipping data fetch")
        return []

    logger.info(f"🎯 S3: Fetching data from {len(regions)} optimized regions: {regions}")
    return await fetch_and_cache_s3_all_regions(regions, session_factory, workspace_id, aws_account_id)