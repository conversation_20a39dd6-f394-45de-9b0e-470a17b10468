import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor


CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "iam")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"iam_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "iam")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of IAM-related resources that checks can consume.
    Following EC2's simple pattern to avoid deadlocks.
    Note: IAM is a global service - only collect data for us-east-1.
    """
    # Since IAM is global, only collect data for us-east-1 to avoid redundant API calls
    if region != "us-east-1":
        return {
            "region": region,
            "policies": {"Policies": []},
            "users": {"Users": []},
            "roles": {"Roles": []},
            "groups": {"Groups": []},
            "password_policy": None,
            "account_summary": None,
        }

    async with session.client(AWSServiceNameEnum.IAM.value, region_name=region) as client:
        # Simple API calls like EC2 - no complex logic or nested operations
        policies = await client.list_policies(Scope='Local')
        users = await client.list_users()
        roles = await client.list_roles()
        groups = await client.list_groups()

        # Get basic account info
        password_policy = None
        account_summary = None
        try:
            password_policy = await client.get_account_password_policy()
        except Exception:
            pass  # Password policy might not exist

        try:
            account_summary = await client.get_account_summary()
        except Exception:
            pass

    return {
        "region": region,
        "policies": policies,
        "users": users,
        "roles": roles,
        "groups": groups,
        "password_policy": password_policy,
        "account_summary": account_summary,
    }


async def fetch_and_cache_iam_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch IAM resources for a region and cache to JSON file `iam_<region>.json`.
    Returns the absolute file path for the region cache.
    Note: IAM is global, but we maintain regional caching for consistency.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_iam_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    IAM is a global service - only fetch data from us-east-1 and replicate to other regions.
    This prevents redundant API calls and ensures consistent global data across all regions.
    Returns a list of cache file paths.
    """
    import logging
    logger = logging.getLogger(__name__)

    # Filter regions to only include valid AWS regions (exclude 'global')
    import re
    region_pattern = re.compile(r"^[a-z]{2}-[a-z]+-\d$")
    valid_regions = []

    logger.info(f"IAM: Original regions received: {regions}")

    for region in regions:
        if not isinstance(region, str) or not region:
            logger.warning(f"IAM: Skipping invalid region (not string or empty): {region}")
            continue
        if region.lower() == 'global':
            logger.warning(f"IAM: Skipping global region - IAM is global service, using us-east-1: {region}")
            continue
        if not region_pattern.match(region):
            logger.warning(f"IAM: Skipping invalid region format: {region}")
            continue
        valid_regions.append(region)

    logger.info(f"IAM: Valid regions after filtering: {valid_regions}")

    if not valid_regions:
        logger.warning("IAM: No valid regions found, defaulting to us-east-1")
        valid_regions = ["us-east-1"]

    # For IAM global service: fetch data only from us-east-1, then replicate to other regions
    primary_region = "us-east-1" if "us-east-1" in valid_regions else valid_regions[0]
    logger.info(f"IAM: Using primary region for data fetch: {primary_region}")

    # Fetch data from primary region
    primary_cache_path = await fetch_and_cache_iam_region_data(
        primary_region, session_factory, workspace_id, aws_account_id
    )

    # Replicate the primary region data to all other regions
    cache_paths = [primary_cache_path]

    for region in valid_regions:
        if region != primary_region:
            region_cache_path = _region_cache_path(workspace_id, aws_account_id, region)
            # Copy the primary region data to this region's cache
            import shutil
            shutil.copy2(primary_cache_path, region_cache_path)
            cache_paths.append(region_cache_path)
            # logger.info(f"IAM: Replicated global data to region: {region}")

    logger.info(f"IAM: Data fetch completed for {len(valid_regions)} regions using global service pattern")
    return cache_paths


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Generic wrapper function that calls the IAM-specific caching function.
    This maintains consistency with other service implementations.
    IAM is treated as a global service - data is fetched once and replicated.
    """
    import logging
    logger = logging.getLogger(__name__)

    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id

    # For IAM (global service), if no regions provided, still fetch from us-east-1
    if not regions:
        logger.info("📭 IAM: No regions with resources found - using us-east-1 for global IAM data")
        regions = ["us-east-1"]  # IAM is global but accessed via us-east-1

    logger.info(f"🎯 IAM: Starting global service data fetch for {len(regions)} regions: {regions}")
    result = await fetch_and_cache_iam_all_regions(regions, session_factory, workspace_id, aws_account_id)
    logger.info(f"✅ IAM: Global service data fetch completed successfully")
    return result
