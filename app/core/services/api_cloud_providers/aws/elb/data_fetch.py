import asyncio
import json
import os
import logging
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

# Set up logging
logger = logging.getLogger(__name__)

CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "elb")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    """Generate cache file path for a specific region"""
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "elb")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"elb_{region}.json")


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of ELB-related resources that checks can consume.
    """
    # logger.info(f"ELB: Collecting resources for region {region}")

    # Initialize data structure
    classic_load_balancers = {"LoadBalancerDescriptions": []}
    load_balancers = {"LoadBalancers": []}
    classic_lb_attributes = {}
    classic_lb_listeners = {}
    lb_attributes = {}
    lb_listeners = {}

    try:
        # Classic Load Balancers (ELB v1)
        async with session.client('elb', region_name=region) as elb_client:
            classic_load_balancers = await elb_client.describe_load_balancers()
            
            # Get attributes and listeners for each classic load balancer
            for lb in classic_load_balancers.get("LoadBalancerDescriptions", []):
                lb_name = lb["LoadBalancerName"]
                
                try:
                    # Get attributes
                    attributes = await elb_client.describe_load_balancer_attributes(
                        LoadBalancerName=lb_name
                    )
                    classic_lb_attributes[lb_name] = attributes
                except Exception as e:
                    logger.warning(f"ELB: Failed to get attributes for classic LB {lb_name}: {e}")
                    classic_lb_attributes[lb_name] = {"LoadBalancerAttributes": {}}
                
                # For Classic Load Balancers, listener information is already in the describe_load_balancers response
                # Extract listeners from the LoadBalancerDescription
                listener_descriptions = lb.get("ListenerDescriptions", [])
                listeners = []
                for listener_desc in listener_descriptions:
                    listener = listener_desc.get("Listener", {})
                    listeners.append(listener)

                classic_lb_listeners[lb_name] = {"Listeners": listeners}
        
        # Application/Network/Gateway Load Balancers (ELB v2)
        async with session.client('elbv2', region_name=region) as elbv2_client:
            load_balancers = await elbv2_client.describe_load_balancers()
            
            # Get listeners and attributes for each v2 load balancer
            for lb in load_balancers.get("LoadBalancers", []):
                lb_arn = lb["LoadBalancerArn"]
                lb_name = lb["LoadBalancerName"]
                
                try:
                    # Get listeners
                    listeners = await elbv2_client.describe_listeners(LoadBalancerArn=lb_arn)
                    lb_listeners[lb_arn] = listeners
                except Exception as e:
                    logger.warning(f"ELB: Failed to get listeners for LB {lb_name}: {e}")
                    lb_listeners[lb_arn] = {"Listeners": []}
                
                try:
                    # Get attributes
                    attributes = await elbv2_client.describe_load_balancer_attributes(
                        LoadBalancerArn=lb_arn
                    )
                    lb_attributes[lb_arn] = attributes
                except Exception as e:
                    logger.warning(f"ELB: Failed to get attributes for LB {lb_name}: {e}")
                    lb_attributes[lb_arn] = {"Attributes": []}

    except Exception as e:
        logger.error(f"ELB: Error collecting resources for region {region}: {e}")
        raise

    return {
        "region": region,
        "classic_load_balancers": classic_load_balancers,
        "load_balancers": load_balancers,
        "classic_lb_attributes": classic_lb_attributes,
        "classic_lb_listeners": classic_lb_listeners,
        "lb_attributes": lb_attributes,
        "lb_listeners": lb_listeners
    }


async def fetch_and_cache_elb_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch ELB resources for a region and cache to JSON file `elb_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_elb_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []
    tasks = [fetch_and_cache_elb_region_data(region, session_factory, workspace_id, aws_account_id) for region in regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Enhanced wrapper function with Resource Explorer optimization for ELB service.
    Uses Resource Explorer to only fetch data from regions where ELB resources exist.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id

    # If no regions provided (Resource Explorer found no ELB resources), return empty list
    if not regions:
        logger.info("📭 ELB: No regions with resources - skipping data fetch")
        return []

    logger.info(f"🎯 ELB: Fetching data from {len(regions)} optimized regions: {regions}")
    return await fetch_and_cache_elb_all_regions(regions, session_factory, workspace_id, aws_account_id)