from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "classic_load_balancer_ssl_security_policy": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_SSL_SECURITY_POLICY.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            classic_load_balancers = (cached.get("classic_load_balancers") or {}).get("LoadBalancerDescriptions", [])
            classic_lb_listeners = cached.get("classic_lb_listeners", {})

            for lb in classic_load_balancers:
                lb_name = lb["LoadBalancerName"]
                listeners_data = classic_lb_listeners.get(lb_name, {})
                listeners = listeners_data.get("Listeners", [])
                
                uses_strong_policy = True
                
                for listener in listeners:
                    if listener.get("Protocol") in ["HTTPS", "SSL"]:
                        ssl_certificate_id = listener.get("SSLCertificateId", "")
                        # Simple check - if it has SSL certificate, consider it compliant
                        if not ssl_certificate_id:
                            uses_strong_policy = False
                            break
                
                if findings["classic_load_balancer_ssl_security_policy"]["status"] == ResourceComplianceStatusEnum.PASS.value and not uses_strong_policy:
                    findings["classic_load_balancer_ssl_security_policy"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["classic_load_balancer_ssl_security_policy"]["details"].append({
                    "load_balancer_name": lb_name,
                    # "uses_strong_ssl_policy": uses_strong_policy,
                    "region": region,
                    "compliance": uses_strong_policy
                })

        return findings

    def remediate(self):
        pass
