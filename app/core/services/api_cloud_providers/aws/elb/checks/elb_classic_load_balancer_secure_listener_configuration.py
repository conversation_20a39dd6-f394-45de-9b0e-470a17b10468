from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "classic_load_balancer_secure_listener_configuration": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_SECURE_LISTENER_CONFIGURATION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            classic_load_balancers = (cached.get("classic_load_balancers") or {}).get("LoadBalancerDescriptions", [])
            classic_lb_listeners = cached.get("classic_lb_listeners", {})

            for lb in classic_load_balancers:
                lb_name = lb["LoadBalancerName"]
                listeners_data = classic_lb_listeners.get(lb_name, {})
                listeners = listeners_data.get("Listeners", [])
                
                has_secure_listener = False
                
                for listener in listeners:
                    protocol = listener.get("Protocol", "")
                    if protocol in ["HTTPS", "SSL"]:
                        has_secure_listener = True
                        break
                
                if findings["classic_load_balancer_secure_listener_configuration"]["status"] == ResourceComplianceStatusEnum.PASS.value and not has_secure_listener:
                    findings["classic_load_balancer_secure_listener_configuration"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["classic_load_balancer_secure_listener_configuration"]["details"].append({
                    "load_balancer_name": lb_name,
                    # "has_secure_listener": has_secure_listener,
                    # "listeners": [{"protocol": l.get("Protocol"), "port": l.get("LoadBalancerPort")} for l in listeners],
                    "region": region,
                    "compliance": has_secure_listener
                })

        return findings

    def remediate(self):
        pass
