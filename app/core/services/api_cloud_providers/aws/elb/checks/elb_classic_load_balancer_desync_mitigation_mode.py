from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "classic_load_balancer_desync_mitigation_mode": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.CLASSIC_LOAD_BALANCER_DESYNC_MITIGATION_MODE.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            classic_load_balancers = (cached.get("classic_load_balancers") or {}).get("LoadBalancerDescriptions", [])
            classic_lb_attributes = cached.get("classic_lb_attributes", {})

            for lb in classic_load_balancers:
                lb_name = lb["LoadBalancerName"]
                attributes_data = classic_lb_attributes.get(lb_name, {})
                attributes = attributes_data.get("LoadBalancerAttributes", {})
                
                # Classic load balancers don't have desync mitigation mode, so they pass by default
                is_compliant = True
                
                findings["classic_load_balancer_desync_mitigation_mode"]["details"].append({
                    "load_balancer_name": lb_name,
                    # "desync_mitigation_mode": "N/A (Classic LB)",
                    "region": region,
                    "compliance": is_compliant
                })

        return findings

    def remediate(self):
        pass
