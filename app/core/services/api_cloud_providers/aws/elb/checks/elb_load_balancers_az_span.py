from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "load_balancers_az_span": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.LOAD_BALANCERS_AZ_SPAN.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            load_balancers = (cached.get("load_balancers") or {}).get("LoadBalancers", [])

            for lb in load_balancers:
                lb_name = lb["LoadBalancerName"]
                lb_arn = lb["LoadBalancerArn"]
                az_count = len(lb.get("AvailabilityZones", []))
                spans_multiple_azs = az_count >= 2
                
                if findings["load_balancers_az_span"]["status"] == ResourceComplianceStatusEnum.PASS.value and not spans_multiple_azs:
                    findings["load_balancers_az_span"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["load_balancers_az_span"]["details"].append({
                    "load_balancer_name": lb_name,
                    # "load_balancer_arn": lb_arn,
                    "type": lb.get("Type"),
                    # "availability_zones": lb.get("AvailabilityZones", []),
                    # "az_count": az_count,
                    # "spans_multiple_azs": spans_multiple_azs,
                    "region": region,
                    "compliance": spans_multiple_azs
                })

        return findings

    def remediate(self):
        pass
