from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "alb_desync_mitigation_mode": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.ALB_DESYNC_MITIGATION_MODE.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            load_balancers = (cached.get("load_balancers") or {}).get("LoadBalancers", [])
            lb_attributes = cached.get("lb_attributes", {})

            for lb in load_balancers:
                if lb.get("Type") == "application":
                    lb_arn = lb["LoadBalancerArn"]
                    lb_name = lb["LoadBalancerName"]
                    attributes_data = lb_attributes.get(lb_arn, {})
                    attributes = attributes_data.get("Attributes", [])
                    
                    desync_mode = next(
                        (attr["Value"] for attr in attributes
                         if attr["Key"] == "routing.http.desync_mitigation_mode"),
                        "defensive"
                    )
                    
                    is_compliant = desync_mode in ["strictest", "defensive"]
                    
                    if findings["alb_desync_mitigation_mode"]["status"] == ResourceComplianceStatusEnum.PASS.value and not is_compliant:
                        findings["alb_desync_mitigation_mode"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["alb_desync_mitigation_mode"]["details"].append({
                        "load_balancer_name": lb_name,
                        # "load_balancer_arn": lb_arn,
                        # "desync_mitigation_mode": desync_mode,
                        "region": region,
                        "compliance": is_compliant
                    })

        return findings

    def remediate(self):
        pass
