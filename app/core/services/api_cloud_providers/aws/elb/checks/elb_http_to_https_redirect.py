from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "http_to_https_redirect": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.HTTP_TO_HTTPS_REDIRECT.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            load_balancers = (cached.get("load_balancers") or {}).get("LoadBalancers", [])
            lb_listeners = cached.get("lb_listeners", {})

            for lb in load_balancers:
                if lb.get("Type") == "application":
                    lb_arn = lb["LoadBalancerArn"]
                    lb_name = lb["LoadBalancerName"]
                    listeners_data = lb_listeners.get(lb_arn, {})
                    listeners = listeners_data.get("Listeners", [])
                    
                    has_redirect = False
                    
                    for listener in listeners:
                        if listener.get("Protocol") == "HTTP":
                            default_actions = listener.get("DefaultActions", [])
                            for action in default_actions:
                                if action.get("Type") == "redirect":
                                    redirect_config = action.get("RedirectConfig", {})
                                    if redirect_config.get("Protocol") == "HTTPS":
                                        has_redirect = True
                                        break
                    
                    if findings["http_to_https_redirect"]["status"] == ResourceComplianceStatusEnum.PASS.value and not has_redirect:
                        findings["http_to_https_redirect"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                    findings["http_to_https_redirect"]["details"].append({
                        "load_balancer_name": lb_name,
                        # "load_balancer_arn": lb_arn,
                        "type": lb.get("Type"),
                        # "has_http_to_https_redirect": has_redirect,
                        "region": region,
                        "compliance": has_redirect
                    })

        return findings

    def remediate(self):
        pass
