from typing import Dict, Any, List
from app.common import SeverityEnum, ResourceComplianceStatusEnum, ELBChecksDescriptionEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor
from app.core.services.api_cloud_providers.aws.elb.data_fetch import read_cached_region_data
import logging

logger = logging.getLogger(__name__)


class Check(BaseChecksProcessor):
    def __init__(self, credentials, regions: List[str], account_id: str):
        super().__init__(credentials, regions)
        self.account_id = account_id
        self.workspace_id = credentials.get("workspace_id")
        self.aws_account_id = credentials.get("aws_account_id")

    def check(self) -> Dict[str, Any]:
        findings: Dict[str, Any] = {
            "load_balancers_deletion_protection": {
                "status": ResourceComplianceStatusEnum.PASS.value,
                "details": [],
                "description": ELBChecksDescriptionEnum.LOAD_BALANCERS_DELETION_PROTECTION.value,
                "severity": SeverityEnum.MEDIUM.value,
            }
        }

        for region in self.regions:
            cached = read_cached_region_data(self.workspace_id, self.aws_account_id, region) or {}
            load_balancers = (cached.get("load_balancers") or {}).get("LoadBalancers", [])
            lb_attributes = cached.get("lb_attributes", {})

            for lb in load_balancers:
                lb_arn = lb["LoadBalancerArn"]
                lb_name = lb["LoadBalancerName"]
                attributes_data = lb_attributes.get(lb_arn, {})
                attributes = attributes_data.get("Attributes", [])
                
                deletion_protection_enabled = next(
                    (attr["Value"] == "true" for attr in attributes
                     if attr["Key"] == "deletion_protection.enabled"),
                    False
                )
                
                if findings["load_balancers_deletion_protection"]["status"] == ResourceComplianceStatusEnum.PASS.value and not deletion_protection_enabled:
                    findings["load_balancers_deletion_protection"]["status"] = ResourceComplianceStatusEnum.FAIL.value

                findings["load_balancers_deletion_protection"]["details"].append({
                    "load_balancer_name": lb_name,
                    # "load_balancer_arn": lb_arn,
                    "type": lb.get("Type"),
                    # "deletion_protection_enabled": deletion_protection_enabled,
                    "region": region,
                    "compliance": deletion_protection_enabled
                })

        return findings

    def remediate(self):
        pass
