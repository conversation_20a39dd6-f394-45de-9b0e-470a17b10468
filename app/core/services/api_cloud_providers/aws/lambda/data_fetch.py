import asyncio
import json
import os
from typing import Any, Dict, List, Callable
from datetime import datetime
from decimal import Decimal
import shutil
import logging

from app.common import AWSServiceNameEnum
from app.core.services.api_cloud_providers.aws.config.base_scan import BaseChecksProcessor

logger = logging.getLogger(__name__)


CACHE_ROOT = os.environ.get("CLOUDAUDIT_CACHE_ROOT", "/tmp/cloudaudit_cache")


class AWSJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle AWS API response objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        # Handle other AWS-specific types if needed
        return super().default(obj)


def _region_cache_path(workspace_id: str, aws_account_id: str, region: str) -> str:
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "lambda")
    os.makedirs(service_dir, exist_ok=True)
    return os.path.join(service_dir, f"lambda_{region}.json")


def clear_cache(workspace_id: str, aws_account_id: str) -> None:
    """
    Remove existing cache directory for this service under the given
    workspace and AWS account. Called at the start of a new scan.
    """
    service_dir = os.path.join(CACHE_ROOT, str(workspace_id), str(aws_account_id), "lambda")
    if os.path.isdir(service_dir):
        shutil.rmtree(service_dir)


async def _collect_region_resources(session, region: str) -> Dict[str, Any]:
    """
    Pure data collection only. No compliance logic here.
    Returns a dict of Lambda-related resources that checks can consume.
    """

    if not isinstance(region, str) or not region or region.lower() == 'global' or '-' not in region:
        raise ValueError(f"Invalid region passed to Lambda fetch: {region!r}")

    async with session.client(AWSServiceNameEnum.Lambda.value, region_name=region) as lambda_client, \
         session.client(AWSServiceNameEnum.EC2.value, region_name=region) as ec2_client:

        functions = await lambda_client.list_functions()

        # Get additional function details for each function
        function_details = {}
        subnet_details = {}  # Cache subnet details to avoid duplicate API calls

        for function in functions.get("Functions", []):
            function_name = function["FunctionName"]
            try:
                # Get function configuration
                config = await lambda_client.get_function_configuration(FunctionName=function_name)
                function_details[function_name] = {
                    "configuration": config
                }

                # Try to get function policy (resource-based policy)
                try:
                    policy_response = await lambda_client.get_policy(FunctionName=function_name)
                    function_details[function_name]["policy"] = policy_response
                except Exception:
                    # Function might not have a resource-based policy
                    function_details[function_name]["policy"] = None

                # If function is in VPC, get subnet details for multi-AZ check
                vpc_config = config.get("VpcConfig", {})
                if vpc_config and vpc_config.get("SubnetIds"):
                    subnet_ids = vpc_config.get("SubnetIds", [])
                    for subnet_id in subnet_ids:
                        if subnet_id not in subnet_details:
                            try:
                                subnet_response = await ec2_client.describe_subnets(SubnetIds=[subnet_id])
                                subnet_details[subnet_id] = subnet_response.get("Subnets", [])
                            except Exception:
                                # If we can't get subnet details, store empty list
                                subnet_details[subnet_id] = []

            except Exception as e:
                # If we can't get details for a function, log and continue
                function_details[function_name] = {
                    "configuration": None,
                    "policy": None,
                    "error": str(e)
                }

    return {
        "region": region,
        "functions": functions,
        "function_details": function_details,
        "subnet_details": subnet_details,
    }


async def fetch_and_cache_lambda_region_data(
    region: str,
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> str:
    """
    Fetch Lambda resources for a region and cache to JSON file `lambda_<region>.json`.
    Returns the absolute file path for the region cache.
    """
    session = session_factory(region)
    cache_path = _region_cache_path(workspace_id, aws_account_id, region)
    # If cache exists, skip re-fetch to ensure single fetch per service scan
    if os.path.exists(cache_path):
        return cache_path
    data = await _collect_region_resources(session, region)
    with open(cache_path, "w") as fp:
        json.dump(data, fp, cls=AWSJSONEncoder, indent=2)
    return cache_path


async def fetch_and_cache_lambda_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    workspace_id: str,
    aws_account_id: str,
) -> List[str]:
    """
    Convenience helper to fetch/cache multiple regions concurrently.
    Returns a list of cache file paths.
    """
    # Filter regions: drop invalid/falsy and 'global'
    regions = [r for r in regions if isinstance(r, str) and r and r.lower() != 'global']
    if not regions:
        return []
    tasks = [fetch_and_cache_lambda_region_data(region, session_factory, workspace_id, aws_account_id) for region in regions]
    return await asyncio.gather(*tasks)


def read_cached_region_data(workspace_id: str, aws_account_id: str, region: str) -> Dict[str, Any] | None:
    """
    Read cached region data from JSON file.
    Returns None if cache file doesn't exist.
    """
    path = _region_cache_path(workspace_id, aws_account_id, region)
    if not os.path.exists(path):
        return None
    with open(path, "r") as fp:
        return json.load(fp)


# Generic service-facing helpers (so Celery can call uniformly across services)

def prepare_session_factory(credentials: dict, regions: List[str]) -> Callable[[str], Any]:
    """
    Prepare a session factory function for creating AWS sessions.
    """
    base = BaseChecksProcessor(credentials, regions)
    def _factory(region: str):
        return base.get_session(region)
    return _factory


async def fetch_and_cache_all_regions(
    regions: List[str],
    session_factory: Callable[[str], Any],
    account_id: str,
    workspace_id: str = None,
    credentials: dict = None,
) -> List[str]:
    """
    Enhanced wrapper function with Resource Explorer optimization for Lambda service.
    Uses Resource Explorer to only fetch data from regions where Lambda resources exist.
    """
    # Extract workspace_id and aws_account_id from credentials
    if credentials:
        workspace_id = credentials.get('workspace_id')
        aws_account_id = credentials.get('aws_account_id')
    else:
        # Fallback: if no credentials provided, use account_id as aws_account_id
        if workspace_id is None:
            raise ValueError("workspace_id is required when credentials are not provided")
        aws_account_id = account_id

    # If no regions provided (Resource Explorer found no Lambda resources), return empty list
    if not regions:
        logger.info("📭 Lambda: No regions with resources - skipping data fetch")
        return []

    logger.info(f"🎯 Lambda: Fetching data from {len(regions)} optimized regions: {regions}")
    return await fetch_and_cache_lambda_all_regions(regions, session_factory, workspace_id, aws_account_id)
