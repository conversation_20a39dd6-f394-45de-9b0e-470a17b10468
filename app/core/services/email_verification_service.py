import secrets
import string
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from app import app
from app.common import (
    get_utc_timestamp, get_encrypted_password,
    OTPExpiredException, OTPNotFoundException, OTPAlreadyVerifiedException, OTPResendCooldownException
)

__all__ = ['EmailVerificationService']

logger = logging.getLogger(__name__)


class EmailVerificationService:
    """Service for handling email verification with secure tokens instead of OTP codes"""
    
    def __init__(self):
        self.conn_pool = app.state.connection_pool
        self.token_length = 64  # Secure token length
        self.expiry_minutes = app.config.OTP_EXPIRY_MINUTES  # Reuse existing config
        self.resend_cooldown_minutes = app.config.OTP_RESEND_COOLDOWN_MINUTES
        self.base_url = app.config.BASE_URL  # Need to add this to config
    
    def generate_verification_token(self) -> str:
        """Generate a secure verification token"""
        # Use cryptographically secure random token
        return secrets.token_urlsafe(self.token_length)
    
    async def create_and_store_verification_token(self, email: str, signup_data: Dict[str, Any]) -> str:
        """
        Create and store verification token for email verification
        
        Args:
            email: User's email address
            signup_data: Dictionary containing signup information (first_name, last_name, password, workspace_name)
            
        Returns:
            str: Generated verification token
        """
        # Check if there's a recent verification request (cooldown period)
        await self._check_resend_cooldown(email)
        
        # Generate secure verification token
        verification_token = self.generate_verification_token()
        
        # Calculate expiry time
        expires_at = datetime.utcnow() + timedelta(minutes=self.expiry_minutes)
        
        # Prepare signup data with encrypted password
        processed_signup_data = signup_data.copy()
        if 'password' in processed_signup_data:
            processed_signup_data['password_hash'] = get_encrypted_password(processed_signup_data['password'])
            del processed_signup_data['password']  # Remove plain password
        
        # Store verification token in database
        from app.core.models.mysql import store_verification_token
        await store_verification_token(
            self.conn_pool,
            email=email,
            verification_token=verification_token,
            expires_at=expires_at,
            signup_data=json.dumps(processed_signup_data)
        )
        
        logger.info(f"Verification token created for email: {email}")
        return verification_token
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify token and return signup data if valid
        
        Args:
            token: Verification token to verify
            
        Returns:
            Dict containing signup data and email if token is valid
            
        Raises:
            OTPNotFoundException: If token is not found
            OTPExpiredException: If token has expired
            OTPAlreadyVerifiedException: If token is already verified
        """
        from app.core.models.mysql import get_verification_token, verify_verification_token
        
        # Get verification token record
        token_record = await get_verification_token(self.conn_pool, verification_token=token)
        
        if not token_record:
            logger.warning(f"Verification token not found: {token[:10]}...")
            raise OTPNotFoundException("Invalid or expired verification link")
        
        if token_record['is_verified']:
            logger.warning(f"Verification token already used: {token[:10]}...")
            raise OTPAlreadyVerifiedException("This verification link has already been used")
        
        # Check if token has expired
        if token_record['expires_at'] < datetime.utcnow():
            logger.warning(f"Verification token expired: {token[:10]}...")
            raise OTPExpiredException("This verification link has expired")
        
        # Mark token as verified
        await verify_verification_token(self.conn_pool, verification_token=token)
        
        # Parse and return signup data
        signup_data = json.loads(token_record['signup_data']) if token_record['signup_data'] else {}
        
        logger.info(f"Verification token verified successfully for email: {token_record['email']}")
        return {
            'email': token_record['email'],
            **signup_data
        }
    
    async def resend_verification_link(self, email: str) -> str:
        """
        Resend verification link for the given email
        
        Args:
            email: User's email address
            
        Returns:
            str: New verification token
            
        Raises:
            OTPResendCooldownException: If trying to resend too quickly
            OTPNotFoundException: If no pending verification found
        """
        from app.core.models.mysql import get_verification_token_by_email
        
        # Check cooldown period
        await self._check_resend_cooldown(email)
        
        # Get existing verification record to retrieve signup data
        existing_verification = await get_verification_token_by_email(self.conn_pool, email=email)
        
        if not existing_verification:
            logger.warning(f"No pending verification found for resend: {email}")
            raise OTPNotFoundException("No pending verification found for this email")
        
        # Parse existing signup data
        signup_data = json.loads(existing_verification['signup_data']) if existing_verification['signup_data'] else {}
        
        # Create new verification token (this will replace the existing one)
        new_token = await self.create_and_store_verification_token(email, signup_data)
        
        logger.info(f"Verification link resent for email: {email}")
        return new_token
    
    def generate_verification_url(self, token: str) -> str:
        """
        Generate the complete verification URL
        
        Args:
            token: Verification token
            
        Returns:
            str: Complete verification URL
        """
        base_url = getattr(app.config, 'BASE_URL', 'http://localhost:8000')
        return f"{base_url}/api/verify-email?token={token}"
    
    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired and verified verification token records
        
        Returns:
            int: Number of records cleaned up
        """
        from app.core.models.mysql import cleanup_expired_verification_tokens
        
        try:
            result = await cleanup_expired_verification_tokens(self.conn_pool)
            count = result.get('affected_rows', 0) if result else 0
            logger.info(f"Cleaned up {count} expired verification token records")
            return count
        except Exception as e:
            logger.error(f"Failed to cleanup expired verification tokens: {str(e)}")
            return 0
    
    async def _check_resend_cooldown(self, email: str) -> None:
        """
        Check if the user is trying to resend verification link too quickly
        
        Args:
            email: User's email address
            
        Raises:
            OTPResendCooldownException: If trying to resend too quickly
        """
        from app.core.models.mysql import get_verification_token_by_email
        
        # Get the most recent verification token for this email
        recent_verification = await get_verification_token_by_email(self.conn_pool, email=email)
        
        if recent_verification:
            # Check if the token was created within the cooldown period
            cooldown_threshold = datetime.utcnow() - timedelta(minutes=self.resend_cooldown_minutes)
            
            if recent_verification['created_at'] > cooldown_threshold:
                remaining_seconds = int((recent_verification['created_at'] + timedelta(minutes=self.resend_cooldown_minutes) - datetime.utcnow()).total_seconds())
                logger.warning(f"Verification link resend attempted too quickly for email: {email}")
                raise OTPResendCooldownException(f"Please wait {remaining_seconds} seconds before requesting a new verification link")
    
    async def get_verification_status(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get verification status for debugging/monitoring purposes
        
        Args:
            email: User's email address
            
        Returns:
            Dict with verification status information or None if no verification found
        """
        from app.core.models.mysql import get_verification_token_by_email
        
        verification_record = await get_verification_token_by_email(self.conn_pool, email=email)
        
        if not verification_record:
            return None
        
        return {
            'email': verification_record['email'],
            'created_at': verification_record['created_at'],
            'expires_at': verification_record['expires_at'],
            'is_verified': verification_record['is_verified'],
            'is_expired': verification_record['expires_at'] < datetime.utcnow(),
            'time_remaining': max(0, int((verification_record['expires_at'] - datetime.utcnow()).total_seconds()))
        }
