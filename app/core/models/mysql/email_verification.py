from .helper import fetch_row, insert, update_row, delete
from ...models import sql_scripts

__all__ = [
    'store_verification_token', 'get_verification_token', 'verify_verification_token', 
    'get_verification_token_by_email', 'cleanup_expired_verification_tokens'
]


async def store_verification_token(conn_pool, email, verification_token, expires_at, signup_data):
    """
    Store verification token in the database
    Uses INSERT ... ON DUPLICATE KEY UPDATE to replace existing token for the same email
    """
    return await insert(
        conn_pool,
        sql_stmt=sql_scripts['store_verification_token'],
        params={
            "email": email,
            "verification_token": verification_token,
            "expires_at": expires_at,
            "signup_data": signup_data
        }
    )


async def get_verification_token(conn_pool, verification_token):
    """
    Get verification token record by token
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_verification_token'],
        params={
            "verification_token": verification_token
        }
    )


async def verify_verification_token(conn_pool, verification_token):
    """
    Mark verification token as verified
    """
    return await update_row(
        conn_pool,
        sql_stmt=sql_scripts['verify_verification_token'],
        params={
            "verification_token": verification_token
        }
    )


async def get_verification_token_by_email(conn_pool, email):
    """
    Get the most recent verification token record by email
    Only returns non-expired, non-verified tokens
    """
    return await fetch_row(
        conn_pool,
        sql_stmt=sql_scripts['get_verification_token_by_email'],
        params={"email": email}
    )


async def cleanup_expired_verification_tokens(conn_pool):
    """
    Clean up expired verification token records and verified tokens older than 1 day
    """
    return await delete(
        conn_pool,
        sql_stmt=sql_scripts['cleanup_expired_verification_tokens'],
        params={}
    )
