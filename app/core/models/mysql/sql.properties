get_user_data=SELECT * from users WHERE email=%(email)s;
add_new_user=INSERT INTO users (email, password_hash, workspace_id, created_at, created_by, first_name, last_name) VALUES (%(email)s, %(password_hash)s, %(workspace_id)s, %(created_at)s, %(created_by)s, %(first_name)s, %(last_name)s);
replace_refresh_token=REPLACE INTO refresh_tokens (user_id, refresh_token, expires_at) VALUES (%(user_id)s, %(refresh_token)s, %(expires_at)s);
get_user_refresh_token=SELECT users.id as user_id, users.email as email, users.workspace_id as workspace_id FROM users JOIN refresh_tokens ON users.id = refresh_tokens.user_id WHERE refresh_tokens.refresh_token = %(refresh_token)s;
update_refresh_token=UPDATE refresh_tokens SET refresh_token = %(refresh_token)s, expires_at = %(expires_at)s WHERE user_id = %(user_id)s;
delete_refresh_token=DELETE FROM refresh_tokens WHERE refresh_token = %(refresh_token)s;
get_cloud_providers=SELECT * from cloud_providers;
get_cloud_provider_by_id=SELECT name from cloud_providers WHERE id=%(cloud_provider_id)s;
check_account_exists=SELECT 1 FROM accounts WHERE cloud_provider_id = %(cloud_provider_id)s AND JSON_EXTRACT(credential_data, '#generic_key_str#') = %(generic_key_val)s LIMIT 1;
add_account=INSERT INTO accounts (account_name, user_id, cloud_provider_id, workspace_id, credential_data, created_at) VALUES (%(account_name)s, %(user_id)s, %(cloud_provider_id)s, %(workspace_id)s, %(credential_data)s, %(created_at)s);
get_accounts=SELECT * from accounts WHERE cloud_provider_id = %(cloud_provider_id)s and workspace_id = %(workspace_id)s;
get_accounts_with_findings=SELECT a.id, a.account_name, a.credential_data->>'$.aws_account_id' as account_id, a.cloud_provider_id , a.created_at, MAX(s.scan_start) as last_scan_date, COUNT(DISTINCT s.id) as total_scans, COUNT(DISTINCT CASE WHEN f.status = 'fail' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed') THEN f.id END) as failed_findings FROM accounts a LEFT JOIN scans s ON a.id = s.account_id LEFT JOIN findings f ON s.id = f.scan_id WHERE a.cloud_provider_id = %(cloud_provider_id)s AND a.workspace_id = %(workspace_id)s GROUP BY a.id, a.account_name, a.credential_data, a.created_at;
get_account_detail=SELECT a.id, a.account_name, a.credential_data->>'$.aws_account_id' as account_id, a.credential_data->>'$.access_key' as access_key, a.credential_data, a.cloud_provider_id, a.created_at FROM accounts a WHERE a.id = %(account_id)s AND (a.user_id = %(user_id)s OR EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id)) LIMIT 1;
get_account_recent_scans=SELECT s.id, s.scan_start, s.scan_end, s.status, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as findings_count, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'fail' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as failed_findings FROM scans s WHERE s.account_id = %(account_id)s ORDER BY s.scan_start DESC LIMIT %(limit)s;
delete_account=DELETE FROM accounts WHERE id = %(account_id)s;
get_services=SELECT id, name from services WHERE cloud_provider_id = %(cloud_provider_id)s AND is_enable = true;
check_running_account_scans=select COUNT(distinct case when s.status = 'running' then a.id end) as active_running_accounts, COUNT(case when s.account_id = %(account_id)s and s.status = 'running' then 1 end) as given_account_running_scans from accounts a left join scans s on a.id = s.account_id where a.user_id = %(user_id)s;
check_scan_from_account=SELECT s.id, s.account_id, s.scan_start, s.status FROM scans s WHERE s.account_id = %(account_id)s LIMIT 1;
create_scan=INSERT INTO scans (account_id, scan_start) VALUES (%(account_id)s, %(scan_start)s);
add_scan_service=INSERT INTO scan_services (scan_id, service_id) VALUES (%(scan_id)s, %(service_id)s);
get_account_credentials=SELECT credential_data, workspace_id from accounts WHERE id = %(account_id)s;
check_scan_completion=SELECT CASE WHEN COUNT(*) = SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) THEN 'all_completed' ELSE NULL END AS status_check FROM scan_services WHERE scan_id = %(scan_id)s;
update_scan_status=UPDATE scans SET status = %(status)s WHERE id = %(scan_id)s;
update_scan=UPDATE scans SET status = %(status)s #update_start_stmt# #update_end_stmt# WHERE id = %(scan_id)s;
update_scan_service=UPDATE scan_services SET status = %(status)s #update_last_scan# WHERE id = %(scan_service_id)s;
get_scan_service=SELECT * FROM scan_services WHERE scan_id = %(scan_id)s AND service_id = %(service_id)s;
update_scan_service_status_from_scan_and_service=UPDATE scan_services SET status = %(status)s WHERE scan_id = %(scan_id)s AND service_id = %(service_id)s;
save_findings=INSERT INTO findings (scan_id, service_id, policy_check, severity, description, status, details, created_at) VALUES (%(scan_id)s, %(service_id)s, %(policy_check)s, %(severity)s, %(description)s, %(status)s, %(details)s, %(created_at)s);
get_findings_for_scan_service_policy_check=SELECT * FROM findings WHERE scan_id = %(scan_id)s AND service_id = %(service_id)s AND policy_check = %(policy_check)s LIMIT 1;
update_findings=UPDATE findings SET status = %(status)s, details = %(details)s WHERE id = %(finding_id)s;
add_user_account=INSERT INTO user_accounts (user_id, account_id) VALUES (%(user_id)s, %(account_id)s);
delete_user=DELETE FROM users WHERE id = %(user_id)s;

fetch_permissions=SELECT id, name from permissions;
fetch_user_permissions=SELECT DISTINCT p.id, p.name FROM permissions p WHERE p.id IN (SELECT rp.permission_id FROM role_permissions rp  JOIN user_roles ur ON rp.role_id = ur.role_id WHERE ur.user_id = %(user_id)s UNION SELECT crp.permission_id FROM custom_role_permissions crp JOIN user_custom_roles ucr ON crp.custom_role_id = ucr.custom_role_id WHERE ucr.user_id = %(user_id)s);
add_custom_role=INSERT INTO custom_roles (name, workspace_id, created_at) VALUES (%(name)s, %(workspace_id)s, %(created_at)s);
add_custom_role_permissions=INSERT INTO custom_role_permissions (custom_role_id, permission_id) VALUES (%(custom_role_id)s, %(permission_id)s);
add_user_custom_role=INSERT IGNORE INTO user_custom_roles (user_id, custom_role_id) VALUES (%(user_id)s, %(custom_role_id)s);
add_user_role=INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (%(user_id)s, %(role_id)s);
get_custom_role=SELECT * FROM custom_roles WHERE id = %(id)s;
get_custom_role_permissions=SELECT permission_id FROM custom_role_permissions WHERE custom_role_id = %(custom_role_id)s;
update_custom_role_name=UPDATE custom_roles SET name = %(name)s WHERE id = %(id)s;
delete_custom_role_permissions=DELETE FROM custom_role_permissions WHERE custom_role_id = %(custom_role_id)s and permission_id = %(permission_id)s;
delete_custom_role=DELETE FROM custom_roles WHERE id = %(id)s;
get_workspace_custom_roles=SELECT cr.id AS id, cr.name AS name, JSON_ARRAYAGG( JSON_OBJECT( 'id', crp.permission_id, 'name', p.name) ) AS permissions FROM custom_roles cr LEFT JOIN custom_role_permissions crp ON cr.id = crp.custom_role_id LEFT JOIN permissions p ON crp.permission_id = p.id WHERE cr.workspace_id = %(workspace_id)s GROUP BY cr.id, cr.name;
get_roles=SELECT r.id AS id, r.name AS name, JSON_ARRAYAGG( JSON_OBJECT( 'id', rp.permission_id, 'name', p.name) ) AS permissions FROM roles r LEFT JOIN role_permissions rp ON r.id = rp.role_id LEFT JOIN permissions p ON rp.permission_id = p.id GROUP BY r.id, r.name;
get_user_by_id=SELECT * FROM users WHERE id = %(user_id)s;
remove_user_role=DELETE FROM user_roles WHERE user_id = %(user_id)s AND role_id = %(role_id)s;
remove_user_custom_role=DELETE FROM user_custom_roles WHERE user_id = %(user_id)s AND custom_role_id = %(custom_role_id)s;
add_workspace=INSERT INTO workspaces (name, created_at, created_by) VALUES (%(name)s, %(created_at)s, %(created_by)s);
update_workspace_user=UPDATE workspaces SET created_by = %(created_by)s WHERE id = %(workspace_id)s;
update_user_admin=UPDATE users SET created_by = %(user_id)s WHERE id = %(user_id)s;
get_roles_of_user=SELECT r.id AS role_id, r.name AS role_name, p.id AS permission_id, p.name AS permission_name FROM user_roles ur JOIN roles r ON ur.role_id = r.id JOIN role_permissions rp ON r.id = rp.role_id JOIN permissions p ON rp.permission_id = p.id WHERE ur.user_id = %(user_id)s;
get_custom_roles_of_user=SELECT cr.id AS role_id, cr.name AS role_name, p.id AS permission_id, p.name AS permission_name FROM user_custom_roles ucr JOIN custom_roles cr ON ucr.custom_role_id = cr.id JOIN custom_role_permissions crp ON cr.id = crp.custom_role_id JOIN permissions p ON crp.permission_id = p.id WHERE ucr.user_id = %(user_id)s;
get_workspace_by_id=SELECT name FROM workspaces WHERE id = %(workspace_id)s;
get_user_scans_count=SELECT COUNT(*) as count FROM scans s JOIN accounts a ON s.account_id = a.id WHERE a.user_id = %(user_id)s OR EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id);
get_user_scans=SELECT s.id, s.scan_start, s.scan_end, s.status, a.account_name, a.credential_data->>'$.aws_account_id' AS account_id, cp.name as cloud_provider, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as findings_count, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'fail' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as failed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'pass' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as passed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'remediated' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as remediated_findings, (SELECT COUNT(*) FROM scan_services ss WHERE ss.scan_id = s.id) as total_services, (SELECT COUNT(*) FROM scan_services ss WHERE ss.scan_id = s.id AND ss.status = 'completed') as completed_services FROM scans s JOIN accounts a ON s.account_id = a.id JOIN cloud_providers cp ON a.cloud_provider_id = cp.id WHERE a.user_id = %(user_id)s OR EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id) ORDER BY s.scan_start DESC LIMIT %(limit)s OFFSET %(offset)s;
get_scan_detail=SELECT s.id, s.scan_start, s.scan_end, s.status, a.account_name, a.credential_data->>'$.aws_account_id' AS account_id, cp.name as cloud_provider, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as findings_count, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'fail' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as failed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'pass' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as passed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = s.id AND f.status = 'remediated' AND EXISTS (SELECT 1 FROM scan_services ss WHERE ss.scan_id = s.id AND ss.service_id = f.service_id AND ss.status = 'completed')) as remediated_findings, (SELECT COUNT(*) FROM scan_services ss WHERE ss.scan_id = s.id) as total_services, (SELECT COUNT(*) FROM scan_services ss WHERE ss.scan_id = s.id AND ss.status = 'completed') as completed_services FROM scans s JOIN accounts a ON s.account_id = a.id JOIN cloud_providers cp ON a.cloud_provider_id = cp.id WHERE s.id = %(scan_id)s AND (a.user_id = %(user_id)s OR EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id));
get_scan_services=SELECT ss.id, ss.service_id, srv.name as service_name, ss.status, ss.last_scanned_at, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = ss.scan_id AND f.service_id = ss.service_id AND EXISTS (SELECT 1 FROM scan_services inner_ss WHERE inner_ss.scan_id = ss.scan_id AND inner_ss.service_id = f.service_id AND inner_ss.status = 'completed')) as findings_count, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = ss.scan_id AND f.service_id = ss.service_id AND f.status = 'fail' AND EXISTS (SELECT 1 FROM scan_services inner_ss WHERE inner_ss.scan_id = ss.scan_id AND inner_ss.service_id = f.service_id AND inner_ss.status = 'completed')) as failed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = ss.scan_id AND f.service_id = ss.service_id AND f.status = 'pass' AND EXISTS (SELECT 1 FROM scan_services inner_ss WHERE inner_ss.scan_id = ss.scan_id AND inner_ss.service_id = f.service_id AND inner_ss.status = 'completed')) as passed_findings, (SELECT COUNT(*) FROM findings f WHERE f.scan_id = ss.scan_id AND f.service_id = ss.service_id AND f.status = 'remediated' AND EXISTS (SELECT 1 FROM scan_services inner_ss WHERE inner_ss.scan_id = ss.scan_id AND inner_ss.service_id = f.service_id AND inner_ss.status = 'completed')) as remediated_findings FROM scan_services ss JOIN services srv ON ss.service_id = srv.id WHERE ss.scan_id = %(scan_id)s;
check_scan_access=SELECT 1 as has_access FROM scans s JOIN accounts a ON s.account_id = a.id WHERE s.id = %(scan_id)s AND (a.user_id = %(user_id)s OR EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id)) LIMIT 1;
get_findings_count=SELECT COUNT(*) as total FROM findings f WHERE #where_clause# ;
get_findings=SELECT f.id, f.scan_id, f.service_id, srv.name as service_name, f.policy_check, f.severity, f.description, f.status, f.created_at, sc.account_id, a.account_name, a.cloud_provider_id, cp.name as cloud_provider_name, a.credential_data->>'$.aws_account_id' as aws_account_id FROM findings f JOIN services srv ON f.service_id = srv.id JOIN scans sc ON f.scan_id = sc.id JOIN accounts a ON sc.account_id = a.id JOIN cloud_providers cp ON a.cloud_provider_id = cp.id WHERE #where_clause# ORDER BY CASE f.status WHEN 'fail' THEN 1 WHEN 'remediated' THEN 2 WHEN 'pass' THEN 3 ELSE 4 END, CASE f.severity WHEN 'critical' THEN 1 WHEN 'high' THEN 2 WHEN 'medium' THEN 3 WHEN 'low' THEN 4 ELSE 5 END LIMIT %(limit)s OFFSET %(offset)s;
get_finding_detail=SELECT f.id, f.scan_id, f.service_id, s.name as service_name, f.policy_check, f.severity, f.description, f.status, f.created_at, f.details, sc.account_id, a.account_name, a.cloud_provider_id, cp.name as cloud_provider_name, a.credential_data->>'$.aws_account_id' as aws_account_id FROM findings f JOIN services s ON f.service_id = s.id JOIN scans sc ON f.scan_id = sc.id JOIN accounts a ON sc.account_id = a.id JOIN cloud_providers cp ON a.cloud_provider_id = cp.id WHERE f.id =  %(finding_id)s AND (EXISTS (SELECT 1 FROM user_accounts ua WHERE ua.user_id = %(user_id)s AND ua.account_id = a.id) OR EXISTS (SELECT 1 FROM accounts acc WHERE acc.id = a.id AND acc.user_id = %(user_id)s)) LIMIT 1;
get_team_members=SELECT * FROM users WHERE workspace_id = %(workspace_id)s ORDER BY created_at DESC;
get_accounts_by_workspace=SELECT id, account_name, cloud_provider_id FROM accounts WHERE workspace_id = %(workspace_id)s;
update_user_password=UPDATE users SET password_hash = %(password_hash)s WHERE id = %(user_id)s;
update_user_info=UPDATE users SET #set_clause# WHERE id = %(user_id)s;
get_workspace_owner=SELECT u.id, u.email, u.first_name, u.last_name, u.workspace_id FROM users u JOIN workspaces w ON u.workspace_id = w.id WHERE w.id = %(workspace_id)s AND w.created_by = u.id LIMIT 1;
transfer_workspace_ownership=UPDATE workspaces SET created_by = %(new_owner_user_id)s WHERE id = %(workspace_id)s AND created_by = %(current_owner_user_id)s;
update_new_owner_admin_status=UPDATE users SET created_by = %(user_id)s WHERE id = %(user_id)s;
get_check_detail=SELECT id, script_path, service_id FROM check_list WHERE service_id = %(service_id)s;
insert_scan_task_tracking=INSERT INTO scan_task_tracking (scan_id, service_id, check_id, task_status, retry_count, max_retries, regions) VALUES (%(scan_id)s, %(service_id)s, %(check_id)s, %(task_status)s, %(retry_count)s, %(max_retries)s, %(regions)s);
update_scan_task_tracking=UPDATE scan_task_tracking SET #set_clause# WHERE scan_id = %(scan_id)s AND service_id = %(service_id)s AND check_id = %(check_id)s;
get_scan_details =SELECT s.id, s.account_id, s.status, ss.id AS scan_service_id, cp.name AS cloud_provider_name FROM scans s LEFT JOIN scan_services ss ON s.id = ss.scan_id JOIN accounts a ON s.account_id = a.id JOIN cloud_providers cp ON a.cloud_provider_id = cp.id WHERE s.id = %(scan_id)s LIMIT 1;
get_service_details=SELECT srv.id, srv.name, cp.name AS cloud_provider_name FROM services srv JOIN cloud_providers cp ON srv.cloud_provider_id = cp.id WHERE srv.id = %(service_id)s LIMIT 1;

update_user_last_login=UPDATE users SET last_login = %(last_login)s WHERE id = %(user_id)s;

# Email OTP Verification queries (deprecated - use email verification tokens instead)
store_otp=INSERT INTO email_otp_verification (email, otp_code, expires_at, signup_data) VALUES (%(email)s, %(otp_code)s, %(expires_at)s, %(signup_data)s) ON DUPLICATE KEY UPDATE otp_code = VALUES(otp_code), expires_at = VALUES(expires_at), signup_data = VALUES(signup_data), is_verified = FALSE, created_at = CURRENT_TIMESTAMP;
get_otp=SELECT * FROM email_otp_verification WHERE email = %(email)s AND otp_code = %(otp_code)s AND expires_at > NOW() AND is_verified = FALSE LIMIT 1;
verify_otp=UPDATE email_otp_verification SET is_verified = TRUE WHERE email = %(email)s AND otp_code = %(otp_code)s AND expires_at > NOW() AND is_verified = FALSE;
get_otp_by_email=SELECT * FROM email_otp_verification WHERE email = %(email)s AND expires_at > NOW() AND is_verified = FALSE ORDER BY created_at DESC LIMIT 1;
cleanup_expired_otps=DELETE FROM email_otp_verification WHERE expires_at < NOW() OR (is_verified = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY));
get_verified_otp=SELECT * FROM email_otp_verification WHERE email = %(email)s AND is_verified = TRUE ORDER BY created_at DESC LIMIT 1;

# Email Verification Token queries (new link-based verification system)
store_verification_token=INSERT INTO email_verification_tokens (email, verification_token, expires_at, signup_data) VALUES (%(email)s, %(verification_token)s, %(expires_at)s, %(signup_data)s) ON DUPLICATE KEY UPDATE verification_token = VALUES(verification_token), expires_at = VALUES(expires_at), signup_data = VALUES(signup_data), is_verified = FALSE, created_at = CURRENT_TIMESTAMP;
get_verification_token=SELECT * FROM email_verification_tokens WHERE verification_token = %(verification_token)s LIMIT 1;
verify_verification_token=UPDATE email_verification_tokens SET is_verified = TRUE WHERE verification_token = %(verification_token)s AND expires_at > NOW() AND is_verified = FALSE;
get_verification_token_by_email=SELECT * FROM email_verification_tokens WHERE email = %(email)s AND expires_at > NOW() AND is_verified = FALSE ORDER BY created_at DESC LIMIT 1;
cleanup_expired_verification_tokens=DELETE FROM email_verification_tokens WHERE expires_at < NOW() OR (is_verified = TRUE AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY));

